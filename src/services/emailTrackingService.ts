import { ApiResponse, apiService } from './api';

// Interface for email tracking statistics response
export interface EmailTrackingStatistics {
  total: number;
  delivered: number;
  opened: number;
  clicked: number;
  bounced: number;
  spam: number;
  unsubscribed: number;
  current_page: number;
  per_page: number;
  from: number;
  to: number;
  last_page: number;
  data: EmailTrackingData[];
}

// Interface for individual email tracking data
export interface EmailTrackingData {
  notification_id: string;
  sent_at: string;
  type: string;
  notifiable_type: string;
  notifiable_id: number;
  email: string;
  recipient_email: string;
  role_name: string;
  opened_at: string | null;
  open_count: number;
  click_count: number;
}

// Email tracking service
export const emailTrackingService = {
  // Get email tracking statistics
  getEmailTrackingStatistics: (
    token: string,
    params: {
      type?: string;
      start_date?: string;
      end_date?: string;
      notification_type?: string;
      email?: string;
      per_page?: number;
      page?: number;
    }
  ): Promise<ApiResponse<EmailTrackingStatistics>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    // Build query parameters
    const queryParams = new URLSearchParams();
    if (params.type) queryParams.append('type', params.type);
    if (params.start_date) queryParams.append('start_date', params.start_date);
    if (params.end_date) queryParams.append('end_date', params.end_date);
    if (params.notification_type) queryParams.append('notification_type', params.notification_type);
    if (params.email) queryParams.append('email', params.email);
    if (params.per_page) queryParams.append('per_page', params.per_page.toString());
    if (params.page) queryParams.append('page', params.page.toString());

    const endpoint = `/api/email-tracking/statistics?${queryParams.toString()}`;

    return apiService<EmailTrackingStatistics>(endpoint, {
      method: 'GET',
      requiresAuth: true,
      headers,
    });
  },
};